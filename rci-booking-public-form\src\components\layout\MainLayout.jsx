import React, { Fragment, useState } from 'react';
import { Container, Row, Col } from 'reactstrap';
import configs from "../../config/config";

const MainLayout = ({ children, langController, lang }) => {
    const [langVar, setLangVar] = useState(true);
    const urlHome = configs.ENVIRONMENT === "DEVELOPMENT" ? configs.BASE_URL_DEV : configs.BASE_URL_PROD;

    const handleLangChange = () => {
        let newLangController = !langVar;
        if(newLangController){
            langController('eng');
        }else{
            langController('esp');
        }
        setLangVar(!langVar);
    }

    const currentYear = new Date().getFullYear();

    return (
        <Fragment>
            <header className="container-fluid topbar">
                <Container>
                    <Row>
                        <Col xs={12} md={6} lg={6} className="column-logo">
                            <a className="navbar-brand" target={'_blank'} rel="noopener noreferrer" href={urlHome}>
                                <img src={process.env.PUBLIC_URL + '/logo-2020.svg'} className="d-inline-block align-top" alt="Transroute" loading="lazy" />
                            </a>
                        </Col>
                        <Col xs={12} md={6} className="column-info">
                            <div className={'info-contact text-center text-lg-right'}>
                                {lang==='eng'?'Reservation Phone:':'Reservacion por Teléfono:'} <a href="tel:+************">+52 624 143 18 44</a><br />
                                Email: <a href="mailto:<EMAIL>"><EMAIL></a>
                            </div>
                            <div className="ml-4">
                                <div
                                    onClick={handleLangChange}>{langVar ? <img src={process.env.PUBLIC_URL + "/flag-mx.png"} loading="lazy" alt="Mexico Flag" /> : <img src={process.env.PUBLIC_URL + "/flag-us.png"} loading="lazy" alt="USA Flag" />}
                                </div>
                            </div>
                        </Col>
                    </Row>
                </Container>
            </header>
            <div className="bar-color-top" />
            {children}
            <footer className={'container-fluid, mt-5'}>
                <Container>
                    <Row>
                        <div xs={12} lg={6}>
                            <p>Transroute © {currentYear}
                            <br />Los Cabos, Baja California Sur, México</p>
                        </div>
                    </Row>
                </Container>
            </footer>
        </Fragment>
    );
}

export default MainLayout;