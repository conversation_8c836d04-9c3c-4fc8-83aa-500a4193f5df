import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { Container, Row, Col } from 'reactstrap';
import moment from 'moment';

import 'moment/locale/cs.js';
import 'moment/locale/es.js';
import 'moment/locale/fr.js';
import 'moment/locale/nl.js';
import { ReservationPolicy } from './reservation-policy';
import SummaryPrice from './summary-price';

export default function Summary({
    title,
    totalPayment,
    data,
    unit,
    lang,
    tripValidator,
    isOneWayAirportDestination,
    isRoundTrip,
    isServiceActive,
    serviceSelected,
    isCalculatingRate = false,
    tarifaBase,
    rateData
}) {
    moment.locale(lang === 'eng' ? 'en' : 'es');

    // console.log(data);

    const {extra_service} = data;

    return (
        <Fragment>
            <Container className="summary-page">
                <Row className="my-3">
                    <Col xs={12} md={8}>
                        <h3>{title}</h3>
                    </Col>
                </Row>
                <Row className="data-information">
                    <Col xs={12} md={6}>
                        <h5 style={{ color: '#000' }}>
                            {lang === 'eng' ? 'Customer information' : 'Información del cliente'}
                        </h5>
                        <h6>
                            {lang === 'eng' ? 'Full name:' : 'Nombre:'}{' '}
                            <span>{data.fullname}</span>
                        </h6>
                        <h6>
                            {lang === 'eng' ? 'Phone:' : 'Telfono:'}{' '}
                            <span>{data.cellphone}</span>
                        </h6>
                        <h6>
                            {lang === 'eng' ? 'E-mail:' : 'Correo:'}{' '}
                            <span>{data.email}</span>
                        </h6>
                        <h5 style={{ color: '#000' }}>
                            {lang === 'eng' ? 'Transport Selected' : 'Unidad Seleccionada'}
                        </h5>
                        <h6>
                            {lang === 'eng' ? 'Vehicle:' : 'Vehiculo:'}{' '}
                            <span>{unit.label}</span>
                        </h6>
                        <h6>
                            {lang === 'eng' ? 'Trip type:' : 'Tipo de Servicio'}{' '}
                            <span>{data.trip_type}</span>
                        </h6>
                        <h6>
                            {lang === 'eng'
                                ? 'Passenger number:'
                                : 'Número de Pasajeros:'}{' '}
                            <span>{data.total_passengers}</span>
                        </h6>
                        <h6>
                            {lang === 'eng'
                                ? 'Pickup Location:'
                                : 'Ubicación de Recogida'}{' '}
                            <span>{data.pickup_location}</span>
                        </h6>
                        <h6>
                            {lang === 'eng' ? 'Destination:' : 'Destino:'}{' '}
                            <span>{data.destination_location}</span>
                        </h6>
                    </Col>
                    <Col xs={12} md={6}>
                        {!tripValidator && !isOneWayAirportDestination && (
                            <>
                                <h5 style={{ color: '#000' }}>
                                    {lang === 'eng'
                                        ? 'Arrival Information:'
                                        : 'Información de Arribo:'}
                                </h5>
                                <h6>
                                    {lang === 'eng'
                                        ? 'Date / Time:'
                                        : 'Fecha / Hora:'}{' '}
                                    <span>
                                        {data.pickup_date} - {data.pickup_time}
                                    </span>
                                </h6>
                                <h6>
                                    {lang === 'eng'
                                        ? 'Flight Number:'
                                        : 'Número de Vuelo:'}{' '}
                                    <span>{data.arrival_flight_number}</span>
                                </h6>
                                <h6>
                                    {lang === 'eng'
                                        ? 'Airline Name:'
                                        : 'Aerolinea:'}{' '}
                                    <span>{data.arrival_airline}</span>
                                </h6>
                                <hr />
                            </>
                        )}
                        {(data.trip_type === 'Round Trip' || isOneWayAirportDestination) && (
                            <>
                                <h5 style={{ color: '#000' }}>
                                    {lang === 'eng'
                                        ? 'Departure information:'
                                        : 'Información de Partida:'}
                                </h5>
                                <h6>
                                    {lang === 'eng'
                                        ? 'Date / Time:'
                                        : 'Fecha / Hora:'}{' '}
                                    <span>
                                        {data.departure_date} - {data.departure_flight_time}
                                    </span>
                                </h6>
                                <h6>
                                    {lang === 'eng'
                                        ? 'Flight Number:'
                                        : 'Número de Vuelo:'}{' '}
                                    <span>{data.departure_flight_number}</span>
                                </h6>
                                <h6>
                                    {lang === 'eng'
                                        ? 'Airline Name: '
                                        : 'Aerolinea:'}{' '}
                                    <span>{data.departure_airline}</span>
                                </h6>
                                <div style={{ border: '1px solid black', borderRadius: '8px', padding: '.5rem' }}>
                                    <h6>
                                        {lang === 'eng'
                                            ? 'Scheduled Hotel Pickup Time must be 3 hours before:'
                                            : 'Recogida en hotel 3 horas antes:'}{' '}
                                        <span>{data.departure_pickup_time_hotel}</span>
                                    </h6>
                                </div>
                            </>
                        )}
                    </Col>
                    <Col xs={12}>
                        <div style={{ border: '1px solid #ddd', borderRadius: '8px', padding: '15px', marginTop: '15px', marginBottom: '15px' }}>
                            <SummaryPrice
                                reserve={data}
                                isServiceActive={isServiceActive}
                                totalPayment={totalPayment}
                                serviceSelected={serviceSelected || (extra_service ? JSON.parse(extra_service) : {})}
                                isCalculatingRate={isCalculatingRate}
                                tarifaBase={tarifaBase}
                                rateData={rateData}
                            />
                        </div>
                    </Col>
                </Row>
            </Container>
            <hr />
            <ReservationPolicy
                isRoundTrip={isRoundTrip}
                isServiceActive={isServiceActive}
                isOneWayAirportDestination={isOneWayAirportDestination}
                serviceSelected={serviceSelected}
            />
        </Fragment>
    );
}

Summary.propTypes = {
    title: PropTypes.string.isRequired,
    totalPayment: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    data: PropTypes.shape({
        fullname: PropTypes.string.isRequired,
        cellphone: PropTypes.string.isRequired,
        email: PropTypes.string.isRequired,
        trip_type: PropTypes.string.isRequired,
        total_passengers: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
        pickup_location: PropTypes.string.isRequired,
        destination_location: PropTypes.string.isRequired,
        pickup_date: PropTypes.string,
        pickup_time: PropTypes.string,
        arrival_flight_number: PropTypes.string,
        arrival_airline: PropTypes.string,
        departure_date: PropTypes.string,
        departure_flight_time: PropTypes.string,
        departure_flight_number: PropTypes.string,
        departure_airline: PropTypes.string,
        departure_pickup_time_hotel: PropTypes.string
    }).isRequired,
    unit: PropTypes.shape({
        label: PropTypes.string
    }).isRequired,
    lang: PropTypes.string.isRequired,
    tripValidator: PropTypes.bool,
    isOneWayAirportDestination: PropTypes.bool,
    isRoundTrip: PropTypes.bool,
    isServiceActive: PropTypes.bool,
    serviceSelected: PropTypes.shape({
        name: PropTypes.string,
        price: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
    })
};