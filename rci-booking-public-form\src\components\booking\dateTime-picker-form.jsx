import { useContext, useEffect, useState } from 'react';
import DatePicker, { registerLocale } from "react-datepicker";
import { FormGroup, InputGroup, InputGroupAddon, InputGroupText, Label, Row } from 'reactstrap'
import { Controller } from 'react-hook-form';
import { ReservationContext } from '../../context/reservation.context';
import es from 'date-fns/locale/es';
import en from 'date-fns/locale/en-US';
import moment from 'moment';
registerLocale("es", es);
registerLocale("en", en);

export const DateTimePickerForm = ({form, setValue, tripValidator, isOneWay, isOneWayAirportDestination, errors, clearErrors, formSubmitted, control}) => {
    const { lang } = useContext(ReservationContext);
    const { trip_type } = form;

//     console.log({form})

    // Inicializar estados con valores del formulario o valores por defecto
    const [pickupDate, setPickupDate] = useState(form.pickup_date !== '' ? new Date(form.pickup_date) : null);
    const [pickupTime, setPickupTime] = useState(form.pickup_time !== '' ? new Date(`01/01/2023 ${form.pickup_time}`) : null);
    const [minArrivalDateToSelect, setMinArrivalDateToSelect] = useState(new Date());
    const [departureDate, setDepartureDate] = useState(form.departure_date ? new Date(form.departure_date) : null);
    const [departureFlightTime, setDepartureFlightTime] = useState(
        form.departure_flight_time ? new Date(`01/01/2023 ${form.departure_flight_time}`) : null
    );
    const [minDepartureDateToSelect, setMinDepartureDateToSelect] = useState(new Date());
    const [minDepartureDateForAirport, setMinDepartureDateForAirport] = useState(new Date());

    useEffect(() => {
        if (form.pickup_date !== '') {
            setPickupDate(new Date(form.pickup_date));
        } else {
            setPickupDate(null);
        }

        if (form.pickup_time !== '') {
            setPickupTime(new Date(`01/01/2023 ${form.pickup_time}`));
        } else {
            setPickupTime(null);
        }

        if (form.departure_date !== '') {
            setDepartureDate(new Date(form.departure_date));
        } else {
            setDepartureDate(null);
        }

        if (form.departure_flight_time !== '') {
            setDepartureFlightTime(new Date(`01/01/2023 ${form.departure_flight_time}`));
        } else {
            setDepartureFlightTime(null);
        }
    }, [form.departure_date, form.departure_flight_time, form.pickup_date, form.pickup_time]);

    ////==========================/ arrival methods
    const handlePickupDate = (date) => {
        if (!date) return;

        setPickupDate(date);
        setDepartureDate(null);

        setValue('pickup_date', moment(date).format("MMMM DD YYYY"));
        setValue('departure_date', '');

        // Clear validation error for pickup date
        clearErrors('pickup_date');
    }

    const handlePickupTime = (time) => {
        if (!time) return;

        setPickupTime(time);
        setValue('pickup_time', moment(time).format("h:mm A"));

        // Clear validation error for pickup time
        clearErrors('pickup_time');
    }

    const getMinimalArrivalDateToSelect = () => {
        const minDate = new Date();
        minDate.setDate(minDate.getDate() + 1);
        setMinArrivalDateToSelect(minDate);
    }

    const getMinimalDepartureDateForAirport = () => {
        const minDate = new Date();
        minDate.setDate(minDate.getDate() + 2); // 48 horas = 2 días
        setMinDepartureDateForAirport(minDate);
    }

    // Función para filtrar horas disponibles cuando es isOneWayAirportDestination
    const filterTimeForAirportDestination = (time) => {
        if (!isOneWayAirportDestination) return true;

        const now = new Date();
        const selectedDate = departureDate || new Date();
        const flightTime = new Date(selectedDate);
        flightTime.setHours(time.getHours(), time.getMinutes(), 0, 0);

        // Calcular la hora de pickup (3 horas antes del vuelo)
        const pickupTime = new Date(flightTime.getTime() - (3 * 60 * 60 * 1000));

        // Calcular 48 horas desde ahora para el pickup
        const minPickupTimeAllowed = new Date(now.getTime() + (48 * 60 * 60 * 1000));

        return pickupTime >= minPickupTimeAllowed;
    }

    useEffect(() => {
        getMinimalArrivalDateToSelect();
        if (isOneWayAirportDestination) {
            getMinimalDepartureDateForAirport();
        }
    }, [isOneWayAirportDestination]);

    ////==========================/ departure methods
    const handleDepatureDate = (date) => {
        if (!date) return;

        setDepartureDate(date);
        setValue('departure_date', moment(date).format("MMMM DD YYYY"));

        // Clear validation error for departure date
        clearErrors('departure_date');
    }

    const handleDepartureFlightTime = (time) => {
        if (!time) return;

        setDepartureFlightTime(time);

        // Calcular la hora de recogida en el hotel (3 horas antes)
        const timeHotelPickup = new Date(time);
        timeHotelPickup.setHours(timeHotelPickup.getHours() - 3);

        setValue('departure_flight_time', moment(time).format("h:mm A"));
        setValue('departure_pickup_time_hotel', moment(timeHotelPickup).format("h:mm A"));

        // Clear validation error for departure flight time
        clearErrors('departure_flight_time');
    }

    const getMinimalDepartureDateToSelect = (date) => {
        if (!date) return;

        const minDate = new Date(date);
        minDate.setDate(minDate.getDate() + 1);
        setMinDepartureDateToSelect(minDate);
    }

    useEffect(() => {
        if (trip_type === 'Round Trip' && pickupDate) {
            getMinimalDepartureDateToSelect(pickupDate);
        }
    }, [trip_type, pickupDate]);

    //////////////////////////////////////////////

return (
    <>
    <Row className="px-0">
        {
        !isOneWayAirportDestination && (<>
                <FormGroup className="pickupDate col-12 col-lg-6">
                        <Label>
                                {lang === 'eng' ? 'Arrival Date' : 'Hora de llegada'}
                        </Label>
                        <InputGroup className="tr-input">
                                <InputGroupAddon addonType="prepend">
                                        <InputGroupText>
                                                <i className="fa fa-calendar" aria-hidden="true"></i>
                                        </InputGroupText>
                                </InputGroupAddon>
                                <Controller
                                        name="pickup_date"
                                        control={control}
                                        rules={{ required: !isOneWayAirportDestination }}
                                        render={({ field, fieldState }) => (
                                                <DatePicker
                                                        {...field}
                                                        locale={lang === 'eng' ? 'en' : 'es'}
                                                        selected={pickupDate}
                                                        onChange={(date) => {
                                                                field.onChange(date ? moment(date).format("MMMM DD YYYY") : '');
                                                                handlePickupDate(date);
                                                        }}
                                                        className={`tr-datepicker ${fieldState.error ? 'is-invalid' : ''}`}
                                                        dateFormat="MM/dd/yyyy"
                                                        minDate={minArrivalDateToSelect}
                                                        autoComplete="off"
                                                        placeholderText={lang === 'eng' ? 'Select date' : 'Seleccionar fecha'}
                                                />
                                        )}
                                />
                        </InputGroup>
                        {errors?.pickup_date && !isOneWayAirportDestination && (
                                <div className="invalid-feedback" style={{display: 'block'}}>
                                        {lang === 'eng' ? 'Please select a pickup date' : 'Por favor seleccione una fecha de recogida'}
                                </div>
                        )}
                </FormGroup>
                <FormGroup className="pickupTime col-12 col-lg-6">
                        <Label>{lang === 'eng' ? 'Arrival Time:' : 'Hora de recogida'}</Label>
                        <InputGroup className="tr-input">
                                <InputGroupAddon addonType="prepend">
                                    <InputGroupText>
                                            <i className="fa fa-clock-o" aria-hidden="true"></i>
                                    </InputGroupText>
                                </InputGroupAddon>
                                <Controller
                                    name="pickup_time"
                                    control={control}
                                    rules={{ required: !isOneWayAirportDestination }}
                                    render={({ field, fieldState }) => (
                                        <DatePicker
                                            {...field}
                                            locale={lang === 'eng' ? 'en' : 'es'}
                                            selected={pickupTime}
                                            onChange={(time) => {
                                                    field.onChange(time ? moment(time).format("h:mm A") : '');
                                                    handlePickupTime(time);
                                            }}
                                            className={`tr-datepicker ${fieldState.error ? 'is-invalid' : ''}`}
                                            showTimeSelect
                                            showTimeSelectOnly
                                            timeIntervals={5}
                                            dateFormat="h:mm aa"
                                            placeholderText={lang === 'eng' ? 'Select time' : 'Seleccionar hora'}
                                        />
                                    )}
                                />
                        </InputGroup>
                        {errors?.pickup_time && !isOneWayAirportDestination && (
                                <div className="invalid-feedback" style={{display: 'block'}}>
                                        {lang === 'eng' ? 'Please select a pickup time' : 'Por favor seleccione una hora de recogida'}
                                </div>
                        )}
                </FormGroup>
                </>
        )
        }
        {
                (form.trip_type === 'Round Trip' || isOneWayAirportDestination) && (
                        <>
                        <FormGroup className="departureDate col-12 col-lg-6">
                                <Label>
                                        {lang === 'eng' ? 'Departure Date:' : 'Fecha partida'}
                                        {!isOneWayAirportDestination && !pickupDate && (
                                                <><br/><small style={{color:'tomato'}}>
                                                        {lang === 'eng' ? 'Select Arrival Date first' : 'Seleccione fecha de recogida primero'}
                                                </small></>
                                        )}
                                </Label>
                                <InputGroup className="tr-input">
                                        <InputGroupAddon addonType="prepend">
                                                <InputGroupText>
                                                        <i className="fa fa-calendar" aria-hidden="true"></i>
                                                </InputGroupText>
                                        </InputGroupAddon>
                                        <Controller
                                                name="departure_date"
                                                control={control}
                                                rules={{ required: form.trip_type === 'Round Trip' || isOneWayAirportDestination }}
                                                render={({ field, fieldState }) => (
                                                        <DatePicker
                                                                {...field}
                                                                locale={lang === 'eng' ? 'en' : 'es'}
                                                                selected={departureDate}
                                                                onChange={(date) => {
                                                                        field.onChange(date ? moment(date).format("MMMM DD YYYY") : '');
                                                                        handleDepatureDate(date);
                                                                }}
                                                                className={`tr-datepicker ${fieldState.error ? 'is-invalid' : ''}`}
                                                                dateFormat="MM/dd/yyyy"
                                                                minDate={isOneWayAirportDestination ? minDepartureDateForAirport : minDepartureDateToSelect}
                                                                disabled={isOneWayAirportDestination ? false : !pickupDate}
                                                                placeholderText={lang === 'eng' ? 'Select date' : 'Seleccionar fecha'}
                                                        />
                                                )}
                                        />
                                </InputGroup>
                                {errors?.departure_date && (
                                        <div className="invalid-feedback" style={{display: 'block'}}>
                                                {lang === 'eng' ? 'Please select a departure date' : 'Por favor seleccione una fecha de partida'}
                                        </div>
                                )}
                        </FormGroup>
                        <FormGroup className="departureFlightTime col-12 col-lg-6">
                                <Label>
                                        {lang === 'eng' ? 'Departure Flight Time:' : 'Hora de partida del vuelo'}
                                        {!isOneWayAirportDestination && !pickupDate && (
                                                <><br/><small style={{color:'tomato'}}>
                                                        {lang === 'eng' ? 'Select Arrival Date first' : 'Seleccione fecha de recogida primero'}
                                                </small></>
                                        )}
                                </Label>
                                <InputGroup className="tr-input">
                                        <InputGroupAddon addonType="prepend">
                                                <InputGroupText>
                                                        <i className="fa fa-clock-o" aria-hidden="true"></i>
                                                </InputGroupText>
                                        </InputGroupAddon>
                                        <Controller
                                                name="departure_flight_time"
                                                control={control}
                                                rules={{ required: form.trip_type === 'Round Trip' || isOneWayAirportDestination }}
                                                render={({ field, fieldState }) => (
                                                        <DatePicker
                                                                {...field}
                                                                locale={lang === 'eng' ? 'en' : 'es'}
                                                                selected={departureFlightTime}
                                                                onChange={(time) => {
                                                                        field.onChange(time ? moment(time).format("h:mm A") : '');
                                                                        handleDepartureFlightTime(time);
                                                                }}
                                                                className={`tr-datepicker ${fieldState.error ? 'is-invalid' : ''}`}
                                                                showTimeSelect
                                                                showTimeSelectOnly
                                                                timeIntervals={5}
                                                                dateFormat="h:mm aa"
                                                                autoComplete="off"
                                                                disabled={isOneWayAirportDestination ? false : !pickupDate}
                                                                filterTime={isOneWayAirportDestination ? filterTimeForAirportDestination : undefined}
                                                                placeholderText={lang === 'eng' ? 'Select time' : 'Seleccionar hora'}
                                                        />
                                                )}
                                        />
                                </InputGroup>
                                {errors?.departure_flight_time && (
                                        <div className="invalid-feedback" style={{display: 'block'}}>
                                                {lang === 'eng' ? 'Please select a departure time' : 'Por favor seleccione una hora de partida'}
                                        </div>
                                )}
                        </FormGroup>

                        </>
                )
        }
    </Row>

    </>
)
}
