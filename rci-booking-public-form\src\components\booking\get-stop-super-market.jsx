import React, { useContext, useState, useEffect } from 'react';
import { Button, Tooltip } from 'reactstrap';
import { ReservationContext } from '../../context/reservation.context';
import styles from './get-stpp-super-market.module.scss';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faShop } from '@fortawesome/free-solid-svg-icons';

export const GetStopSuperMarket = ({ data, setTotalPayment, setIsServiceActive, setIsStopatStore, totalPayment, args, serviceSelected, setServiceSelected }) => {
  const { reservation } = useContext(ReservationContext);
  const { extra_service } = reservation;

  // Se utiliza la prop "totalPayment" para habilitar o deshabilitar los botones según ciertas condiciones
  const [disabledButton, setDisabledButton] = useState(true);
  // Estado para identificar el servicio actualmente seleccionado (radio button)
  const [selectedServiceId, setSelectedServiceId] = useState(null);
  // Estados independientes para cada tooltip, usando un objeto que mapea id's de servicio a boolean
  const [tooltipOpenState, setTooltipOpenState] = useState({});

  // Efecto para determinar si los botones deben estar deshabilitados
  useEffect(() => {
    // Los botones están deshabilitados si no hay un precio total o si el destino es el aeropuerto
    const isDisabled = !totalPayment || data.destination_location === 'Airport SJD';
    setDisabledButton(isDisabled);

    // Si los botones están deshabilitados y hay un servicio seleccionado, lo desactivamos
    if (isDisabled && selectedServiceId) {
      setSelectedServiceId(null);
      setIsServiceActive(false);
      setIsStopatStore(false);
      setServiceSelected({});
    }
  }, [totalPayment, data.destination_location, selectedServiceId, setIsServiceActive, setIsStopatStore, setServiceSelected]);

  // Inicializar el estado del servicio seleccionado basado en las props
  useEffect(() => {
    // Si hay un servicio activo en las props pero no hay ninguno seleccionado localmente
    if (serviceSelected && Object.keys(serviceSelected).length > 0 && serviceSelected.id && !selectedServiceId) {
      setSelectedServiceId(serviceSelected.id);
    }
    // Si no hay servicio activo en las props pero hay uno seleccionado localmente
    else if ((!serviceSelected || Object.keys(serviceSelected).length === 0) && selectedServiceId) {
      setSelectedServiceId(null);
    }
  }, [serviceSelected, selectedServiceId]);

  // Función para alternar el estado del tooltip
  const toggleTooltip = (id) => {
    setTooltipOpenState(prev => ({ ...prev, [id]: !prev[id] }));
  };

  // Función para manejar el clic en un botón de servicio
  const handleButtonClick = (id) => {
    // Encontrar el servicio seleccionado
    const clickedService = extra_service?.find(service => service.id === id);

    if (!clickedService) return;

    if (selectedServiceId === id) {
      // Si el servicio ya estaba seleccionado, lo desactivamos
      setTotalPayment(Number(totalPayment) - Number(clickedService.price));
      setIsStopatStore(false);
      setIsServiceActive(false);
      setSelectedServiceId(null);
      setServiceSelected({});
    } else {
      // Si hay un servicio previamente seleccionado, lo desactivamos primero
      let newTotalPayment = Number(totalPayment);

      if (selectedServiceId) {
        const previousService = extra_service.find(s => s.id === selectedServiceId);
        if (previousService) {
          newTotalPayment -= Number(previousService.price);
        }
      }

      // Activamos el nuevo servicio seleccionado
      newTotalPayment += Number(clickedService.price);
      setTotalPayment(newTotalPayment);
      setIsStopatStore(true);
      setIsServiceActive(true);
      setSelectedServiceId(clickedService.id);
      setServiceSelected(clickedService);
    }
  };

  return (<>
    <h3>Extra Services:</h3>
    <p>The "Stop at Supermarket or Grocery Store" service is only available for hotel destinations.</p>
    <div className={styles.container}>
        {extra_service && extra_service.map(service => {
          const isActive = selectedServiceId === service.id;
          return (
            <React.Fragment key={service.id}>
              <div className={styles.service_container}>
                <div className={styles.icon_container}>
                  <FontAwesomeIcon icon={faShop} />
                </div>
                <div>
                  <h6 style={{fontWeight: '600'}}>{service.name}</h6>
                  <p style={{fontWeight: 'bold', color: '#fa5454'}}>Time to Stop: {service.time}</p>
                </div>
                <h5 style={{ color: '#000000', textAlign:'center' }}>${Number(service.price).toFixed(2)} USD</h5>
                <Button
                  id={`disableButton-${service.id}`}
                  color={isActive ? "danger" : "primary"}
                  onClick={() => handleButtonClick(service.id)}
                  disabled={disabledButton}
                >
                  {isActive ? 'Remove' : 'Add'}
                </Button>
                {disabledButton && (
                  <Tooltip
                    {...args}
                    isOpen={tooltipOpenState[service.id] || false}
                    target={`disableButton-${service.id}`}
                    toggle={() => toggleTooltip(service.id)}
                    placement="top"
                  >
                    Please select Pickup and Destination first. The destination must be a hotel.
                  </Tooltip>
                )}
              </div>
              <hr />
            </React.Fragment>
          );
        })}
      </div>
    </>
  );
};

GetStopSuperMarket.args = {
  autohide: true,
  flip: true,
};

GetStopSuperMarket.argTypes = {
  placement: {
    control: { type: 'select' },
    options: ['top', 'left', 'right', 'bottom'],
  },
};
