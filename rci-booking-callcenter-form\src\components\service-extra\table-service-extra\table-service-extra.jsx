import React, { Fragment, useMemo } from "react";
import ReactDatatable from "@ashvin27/react-datatable";
import * as XLSX from 'xlsx';
import * as FileSaver from 'file-saver';
import "font-awesome/css/font-awesome.min.css";

const TableServiceExtra = ({ handleDetails, records }) => {

    // console.log(records);
  const columns = [    
    {
        key: "ID",
        text: "ID Reserve",
        align: "center",
        sortable: true,
    },
    {
        key: "CORREO",
        text: "Email",
        align: "center",
        sortable: true,
    },
    {
        key: "NOMBRE",
        text: "Name",
        align: "center",
        sortable: true,
    },
    {
        key: "TRANSPORTE",
        text: "Fleet Transport",
        align: "center",
        sortable: true,
    },
    {
        key: "SERVICE_EXTRA_NAME",
        text: "Extra Service Name",
        align: "center",
        sortable: true,
    },
    {
        key: "SERVICE_EXTRA_TIME",
        text: "Extra Service Time",
        align: "center",
        sortable: true,
    },
    {
        key: "SERVICE_EXTRA_PRICE",
        text: "Extra Service Price",
        align: "center",
        sortable: true,
    },
    {
        key: "action",
        text: "Action",
        width: 125,
        align: "center",
        className: "action",
        sortable: false,
        cell: (record) => {
            return (
                <Fragment key={record.ID}>
                    <button className="btn btn-dark btn-sm" style={{ marginRight: "4px" }} onClick={() => handleDetails(record)}>
                        <i className="fa fa-info-circle"></i>
                    </button>
                </Fragment>
            );
        },
    },
  ];

  const config = useMemo(() => ({
    page_size: 50,
    length_menu: [50, 100, 200],
    button: { excel: false },
    sort: { column: "ID", order: "desc" },//sort: {column:"", order:"desc"}
   }), []);

  const exportExcel = () => {
          const ws = XLSX.utils.json_to_sheet(records);
          const wb = { Sheets: { data: ws }, SheetNames: ['data'] };
          const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
          const blob = new Blob(
              [excelBuffer],
              { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8;' }
          );
          FileSaver.saveAs(blob, 'services-extra.xlsx');
      };

  return (<>
    <div className="medium-4 cell d-flex justify-content-end align-items-end">
        <button type="button" className="button btn-green" onClick={exportExcel}>Export to Excell</button>
    </div>
    <ReactDatatable
      className="table stack b-white hover table-bordered"
      key={"table22"}
      config={config}
      records={records}
      columns={columns}
      />
    </>
  );
};

export default TableServiceExtra;
