# Configuración de Valores Predeterminados - Formulario de Booking

## Resumen de Cambios

Se han configurado valores predeterminados en el formulario de booking para mejorar la experiencia del usuario, estableciendo automáticamente las opciones más comunes.

## Valores Predeterminados Configurados

### 1. T<PERSON>o <PERSON>je (Trip Type)
- **Valor**: `Round Trip` (Ida y Vuelta)
- **Estado auxiliar**: `auxTripType` inicializado con Round Trip
- **Estados relacionados**:
  - `isOneWay`: `false`
  - `isRoundTrip`: `true`

### 2. Vehí<PERSON>lo (Vehicle)
- **Valor preferido**: `Small Suv`
- **Fallback**: Primer vehículo disponible en la lista
- **Lógica**: Se busca "Small Suv" en la respuesta de la API, si no existe se usa el primer vehículo
- **Campos actualizados**:
  - `unit`: Nombre del vehículo
  - `unit_id`: ID del vehículo
  - `passenger_number`: Capacidad del vehículo

### 3. Ubicación de Recogida (Pickup Location)
- **Valor**: `Airport SJD`
- **ID**: `1`
- **Configuración**: Se establece automáticamente al cargar las ubicaciones desde la API

### 4. Número de Pasajeros
- **Valor**: `1` pasajero
- **Campo**: `total_passengers`

## Implementación Técnica

### Configuración en useForm
```javascript
const { control, handleSubmit, watch, setValue, getValues, formState: { errors, isSubmitted }, trigger, clearErrors } = useForm({
    defaultValues: {
        ...reservation,
        trip_type: 'Round Trip',
        pickup_location: 'Airport SJD',
        pickup_id: 1,
        unit: 'Small Suv',
        unit_id: 1, // Se ajusta dinámicamente
        passenger_number: 4, // Se ajusta según el vehículo
        total_passengers: 1
    },
    mode: 'onChange'
});
```

### Estados Auxiliares Inicializados
```javascript
// Trip Type preseleccionado
const [auxTripType, setAuxTripType] = useState({ 
    value: "Round Trip", 
    label: lang === "eng" ? "Round Trip" : "Ida y Vuelta" 
});

// Estados de tipo de viaje
const [isOneWay, setIsOneWay] = useState(false);
const [isRoundTrip, setIsRoundTrip] = useState(true);
```

### Lógica de Inicialización de Vehículos
```javascript
const getAllUnits = useCallback(() => {
    getUnits().then((response) => {
        if (response.data.length > 0) {
            let _units = response.data;
            setUnits(_units);

            // Buscar Small Suv o usar el primer vehículo como fallback
            const smallSuvUnit = _units.find(unit => unit.label === 'Small Suv') || _units[0];
            const smallSuvOption = auxUnitOptions.find(option => option.label === 'Small Suv') || auxUnitOptions[0];

            setUnitSelected(smallSuvUnit);
            setValue('unit', smallSuvUnit.label);
            setValue('passenger_number', smallSuvUnit.capacity);
            setValue('unit_id', smallSuvUnit.id_unit);
            setAuxUnit(smallSuvOption);
        }
    });
}, [setValue]);
```

### Lógica de Inicialización de Ubicaciones
```javascript
const getAllLocations = () => {
    getLocations().then((response) => {
        setLocations(response.data);
        
        // Establecer Airport SJD como pickup location predeterminado
        const airportSJDOption = response.data.find(locationGroup =>
            locationGroup.options.some(option =>
                option.value === "Airport SJD" && option.id_zone === 1
            )
        )?.options.find(option =>
            option.value === "Airport SJD" && option.id_zone === 1
        );

        if (airportSJDOption) {
            setValue('pickup_location', airportSJDOption.value);
            setValue('pickup_id', airportSJDOption.id_zone);
            setAuxPickup(airportSJDOption);
        }
    });
};
```

## Componentes Actualizados

### Input de Número de Pasajeros
Se actualizó para usar Controller de react-hook-form:
```javascript
<Controller
    name="total_passengers"
    control={control}
    render={({ field }) => (
        <Input 
            {...field}
            type="select" 
            id="total_passengers"
        >
            {numPassengers.map((item, pos) => (
                <option key={pos} value={item}>{item}</option>
            ))}
        </Input>
    )}
/>
```

## Beneficios de los Valores Predeterminados

### 1. **Mejor Experiencia de Usuario**
- Formulario parcialmente completado al cargar
- Reduce el número de clics necesarios
- Opciones más comunes preseleccionadas

### 2. **Flujo Optimizado**
- Round Trip es el tipo de viaje más común
- Airport SJD es el punto de partida típico
- Small Suv es un vehículo popular

### 3. **Consistencia**
- Comportamiento predecible del formulario
- Estados sincronizados entre form data y UI
- Validaciones coherentes desde el inicio

## Consideraciones Técnicas

### Manejo de Errores
- Si "Small Suv" no existe, se usa el primer vehículo disponible
- Si "Airport SJD" no se encuentra, el campo queda vacío
- Los valores predeterminados no interfieren con las validaciones

### Compatibilidad
- Los valores predeterminados son compatibles con el contexto de reservación existente
- Se mantiene la funcionalidad de cambio dinámico de valores
- No afecta la lógica de cálculo de tarifas

### Rendimiento
- Los valores se establecen una sola vez al cargar los datos
- No hay re-renders innecesarios
- La inicialización es eficiente y rápida
