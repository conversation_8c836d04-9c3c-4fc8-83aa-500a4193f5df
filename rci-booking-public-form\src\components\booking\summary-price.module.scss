@use '../../scss/variables' as vars;

.reservationSummary {
    padding: 20px;
    border: 1px solid vars.$accent-color-1;
    border-radius: 8px;
    width: 100%;
    margin: 20px auto;
}
  
.summaryServiceTitle {
  font-size:20px;
  font-weight: 400;
  span {
      font-weight: bold;
  }
}

.summaryTitle {
  font-size: 1.5em;
  margin-bottom: 15px;
  color: #333;
  text-align: center;
}
  
.summaryItem {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  align-items: flex-end;
}
  .itemTitle {
    color: #555;
  }
  
  .itemValue {
    color: #333;
    font-weight: 600;
  }
  
  .promoPrice {
    color: vars.$accent-color-1;
  }
  
  .totalTitle {
    font-size: 2em;
  }
  
  .totalPrice {
    font-size: 2em;
    font-weight: 700;
  }
  
  .discount {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    background-color: #e6f7ff;
    padding: 10px;
    border-radius: 5px;
  }
  
  .discountLabel {
    font-weight: bold;
    color: #007bff;
  }
  
  .discountValue {
    color: #007bff;
  }
  .extra_service_active {
    display: grid;
    grid-template-columns: 70% 30%;
    .name {
      font-weight:600;
    }
    .price {
        font-weight: 600;
        justify-self: end;
        align-self: center;
      }
    }
.brown {
  color: brown;
}
    
.originalPrice {
  text-decoration: line-through;
  color: #888;
}
    // Estilos para el banner promocional
.promotionalBanner {
    background-color: #f1fff1;
    border: 1px solid #0d65bc;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    margin-bottom: 15px;
    font-size: 14px;
}

.bannerHeader {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.checkIcon {
    color: #28a745 !important;
    font-weight: bold;
    margin-right: 8px;
    font-size: 16px;
}

.bestValue {
    color: #28a745 !important;
    font-weight: 600;
    font-size: 21px;
}

.bannerContent {
    .oneWayPrice {
        color: #6c757d;
        margin-bottom: 5px;
        font-size: 18px;

        .priceValue {
            color: #333;
            font-weight: 600;
        }
    }

  }
.savings {
    .savingsText {
        color: #28a745;
        font-weight: 600;
        span {
          color: #28a745;
          font-weight: 700;
        }
    }
}
