import React, { useState, useEffect } from "react";
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { handlelogout, getUserLvl } from "../../session/session_controller";

const Header = () => {
    const [userlvl] = useState(getUserLvl());

    const handleClickLogOut = () => {
        handlelogout();
    };

    // Initialize Foundation dropdown after component mounts
    useEffect(() => {
        // Check if Foundation is available globally
        if (window.Foundation) {
            window.Foundation.reInit('dropdown-menu');
        }
    }, []);

    return (
        <div id="top-bar" className="full align-self-bottom">
            <div className="grid-container">
                <div className="grid-x grid-padding-x grid-padding-y">
                    <Link to="/reserve"
                        className="small-12 medium-3 cell small-only-text-center  medium-text-left large-text-left"
                    >
                        <img
                            className="logo-header"
                            height="30"
                            src={process.env.PUBLIC_URL + "/logo-2020.svg"}
                            alt="RCI"
                        />
                    </Link>
                    {/* el siguiente menu solo aparecerá en el formulario y las tablas */}
                    <div className="small-12 medium-9 cell align-self-middle text-right large-text-right">
                        <ul className="dropdown menu align-right" data-dropdown-menu>
                            <li>
                                <Link to="/reserve">Booking Form</Link>
                            </li>
                            {userlvl === "3" ? (
                                <li></li>
                            ) : (<>
                            <li>
                                <ul className="menu">
                                    <li>
                                        <Link to="/reservations">Call Center</Link>
                                    </li>
                                    <li>
                                        <Link to="/public-reservations">Public</Link>
                                    </li>
                                </ul>
                            </li>
                            </>
                            )}
                            {userlvl === "3" ? (
                                <li></li>
                            ) : (
                                <li>
                                    <Link to="/fees">Fees</Link>
                                </li>
                            )}
                            {userlvl === "3" ? (
                                <li></li>
                            ) : (
                                <li>
                                    <Link to="/fees-public">Fees Public</Link>
                                </li>
                            )}
                            {userlvl === "3" ? (
                                <li></li>
                            ) : (
                                <li>
                                    <Link to="/extra-service">Extra Services</Link>
                                </li>
                            )}
                            {userlvl === "4" ? (
                                <li>
                                    <Link to="/admin/error-logs" style={{color: '#ff6b6b'}}>
                                        ERROR LOGS
                                    </Link>
                                </li>
                            ) : (
                                <li></li>
                            )}
                            <li>
                                <Link to="/" onClick={handleClickLogOut}>
                                    <FontAwesomeIcon icon="sign-out-alt" /> Log out
                                </Link>
                            </li>
                        </ul>
                    </div>
                    {/* fin del formulario */}
                </div>
            </div>
        </div>
    );
};

export default Header;
