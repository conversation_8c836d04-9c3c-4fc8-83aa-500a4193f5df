import React, { Fragment, useState } from "react";
import Swal from "sweetalert2";
import { loadStripe } from "@stripe/stripe-js";
import { Elements, CardElement, useStripe, useElements } from "@stripe/react-stripe-js";
import LoadingOverlay from "react-loading-overlay";
import configs from "../../config/config";
import { stripePayment } from "../../api_controllers/payment_controller";
import { Button } from "reactstrap";
import styles from './payment-stripe.module.scss';

const key =
    configs.ENVIRONMENT === "DEVELOPMENT"
        ? configs.PK_TEST_BTT_STRIPE
        : configs.PK_LIVE_BTT_STRIPE;

const stripePromise = loadStripe(key);

function CheckoutForm({ lang, totalPayment, handlePayNow }) {
    const stripe = useStripe();
    const elements = useElements();
    const [loaderController, setLoaderController] = useState(false);
    const [message, setMessage] = useState('');

    const pay = async () => {
        if (loaderController) return;
        setLoaderController(true);
        setMessage('');

        if (!stripe || !elements) {
            setMessage("Stripe is not loaded yet. Please wait...");
            setLoaderController(false);
            return;
        }

        const cardElement = elements.getElement(CardElement);
        if (!cardElement) {
            setMessage("Payment form is not ready. Please refresh the page.");
            setLoaderController(false);
            return;
        }

        try {
            const { error, paymentMethod } = await stripe.createPaymentMethod({
                type: "card",
                card: cardElement,
            });

            if (error) {
                setMessage(error.message);
                Swal.fire("Oops!", error.message + "... try again later.", "error");
                setLoaderController(false);
                return;
            }

            const { id } = paymentMethod;
            const data = {
                id,
                amount: Math.round(totalPayment * 100), // centavos
                description: localStorage.getItem("desc"),
            };

            stripePayment(data)
                .then((response) => {
                    const res = response.data;
                    setMessage(res.message);
                    if (res.valid === true) {
                        handlePayNow(res.payment.id, res);
                    } else {
                        Swal.fire("Oops!", res.message || "Payment failed.", "error");
                    }
                    setLoaderController(false);
                })
                .catch((error) => {
                    setMessage(`${error.message}, charge is declined... try again later.`);
                    Swal.fire("Oops!", error.message || "Something went wrong, charge is declined... try again later.", "error");
                    setLoaderController(false);
                });
        } catch (error) {
            setMessage(error.message || "Unknown error");
            Swal.fire("Oops!", error.message + "... try again later.", "error");
            setLoaderController(false);
        }
    };

    return (
        <Fragment>
            <h6 style={{ textAlign: "left" }}>
                {lang === "eng" ? "Pay only with Visa and Master Card" : "Solo Visa y Master Carda en este método de pago"}
            </h6>
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'end' }}>
                <img src={`/${process.env.PUBLIC_URL}stripe-logo-secure-payment-5.png`} width={200} alt="Stripe Secure" />
            </div>
            <div className="Copy__footer">
                <figure className="PaymentLogoGrid" style={{gap:'1rem', marginTop:'1rem', marginBottom:'1rem'}}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="42" height="24" viewBox="0 0 42 24" className="PaymentLogo variant-- ">
                        <title>Visa</title>
                        <path
                            fill="var(--paymentLogoColor, #191E70)"
                            d="M20.8 5.31L17.97 18.9h-3.43l2.83-13.58h3.43zm14.23 8.78l1.82-5.12 1.01 5.12h-2.83zm3.84 4.8H42L39.27 5.31h-2.92c-.61 0-1.22.42-1.42 1.05l-5.05 12.53h3.54l.7-1.98h4.35l.4 1.98zm-8.88-4.49c0-3.55-4.75-3.76-4.75-5.33.1-.73.7-1.15 1.41-1.15 1.11-.1 2.33.1 3.34.63l.6-2.92A8.36 8.36 0 0 0 27.46 5c-3.33 0-5.75 1.88-5.75 4.5 0 1.98 1.71 3.02 2.92 3.65 1.32.62 1.82 1.04 1.72 1.67 0 .94-1 1.36-2.02 1.36a8.37 8.37 0 0 1-3.53-.84l-.6 2.92c1.2.53 2.52.74 3.73.74 3.73.1 6.06-1.78 6.06-4.6zM15.95 5.31L10.5 18.9H6.87L4.14 8.03c0-.52-.4-.94-.8-1.15A11.5 11.5 0 0 0 0 5.73l.1-.42h5.76c.8 0 1.4.63 1.51 1.36l1.41 7.83 3.64-9.19h3.53z"></path>
                    </svg>                    
                    <title>Mastercard</title>
                    <img src={process.env.PUBLIC_URL + '/mastercard-logo.png'} width={50} height="auto" alt="" />
                </figure>
                </div>
                <LoadingOverlay className="loading" active={loaderController} spinner text="Processing data...">
                    <CardElement />
                    <div className="shield mt-2">
                        {lang === "eng"
                            ? "Your payments are made securely with 256-bit encryption"
                            : "Tus pagos son realizados de manera segura con encriptación de 256-bits"}
                        {message &&
                            <h5 style={{
                                color: 'orange',
                                border: '1px solid orange',
                                padding: '1rem',
                                borderRadius: '1rem',
                                textAlign: 'center'
                            }}>{message}</h5>
                        }
                    </div>
                    <div className={`mt-3 buttons-payments`}>
                        <Button
                            color="primary"
                            className={styles.button_pay}
                            onClick={pay}
                            disabled={loaderController || !stripe || !elements}
                        >
                            {lang === "eng" ? "PAY NOW" : "PAGAR AHORA"}
                        </Button>
                    </div>
                </LoadingOverlay>
            </Fragment>
        );
    }

export default function PaymentStripe({ lang, totalPayment, handlePayNow }) {
    return (
        <div>
            <Elements stripe={stripePromise}>
                <CheckoutForm
                    lang={lang}
                    totalPayment={totalPayment}
                    handlePayNow={handlePayNow}
                />
            </Elements>
        </div>
    );
}
