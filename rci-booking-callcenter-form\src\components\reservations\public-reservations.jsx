import React, { Component, Fragment } from "react";
import Header from "../utils/header";
import Footer from "../utils/footer";
import axios from "axios";
import configs from "../../configs/config";
import ReactDatatable from "@ashvin27/react-datatable";
import * as XLSX from 'xlsx';
import * as FileSaver from 'file-saver';
import "font-awesome/css/font-awesome.min.css";
import { <PERSON><PERSON>, ModalHeader, ModalBody, ModalFooter } from "reactstrap";
import LoadingOverlay from "react-loading-overlay";
import Swal from "sweetalert2";
import moment from "moment";

const loaderStyles = {
    overlay: (base) => ({
        ...base,
        background: "rgba(64, 64, 66, 0.9)",
    }),
};

class PublicReservations extends Component {
    constructor(props) {
        super(props);
        this.state = {
            reservations: [],
            isloading: false,
            modalIsOpen: false,
            selected_res: {},
        };
    }

    componentDidMount() {
        this.fetchPublicReservations();
    }

    fetchPublicReservations = () => {
        this.setState({ isloading: true });

        axios.get(`${configs.URL_API_PUBLIC_BASE}/reservations`)
            .then((res) => {
                if (res.data && res.data.results) {
                    const mappedReservations = res.data.results.map((item) => ({
                        ID: item.id_reservation,
                        FOLIO: item.folio,
                        PAYMENT_ID: item.payment_id,
                        TRIP_TYPE: item.trip_type,
                        UNIT: item.unit,
                        PICKUP_LOCATION: item.pickup_location,
                        DESTINATION_LOCATION: item.destination_location,
                        TOTAL_PASSENGERS: item.total_passengers,
                        FULLNAME: item.fullname,
                        MEMBER_ID: item.member_id,
                        EMAIL: item.email,
                        CELLPHONE: item.cellphone,
                        ARRIVAL_DATETIME: item.arrival_datetime,
                        ARRIVAL_AIRLINE: item.arrival_airline,
                        ARRIVAL_FLIGHT_NUMBER: item.arrival_flight_number,
                        DEPARTURE_DATETIME: item.departure_datetime,
                        DEPARTURE_AIRLINE: item.departure_airline,
                        DEPARTURE_FLIGHT_NUMBER: item.departure_flight_number,
                        HOTEL_DEPARTURE_TIME: item.hotel_departure_time,
                        EXTRA_SERVICE: item.extra_service,
                        OBSERVATIONS: item.observations,
                        PAYMENT_METHOD: item.payment_method,
                        DISCOUNT_CODE: item.discount_code,
                        DISCOUNT_PERCENT: item.discount_percent,
                        TOTAL_PAYMENT: item.total_payment,
                        BASE_PRICE: item.base_price,
                        FEE_RCI: item.fee_rci,
                        FEE_TR: item.fee_tr,
                        ACTIVE: item.active,
                        CREATED_AT: item.createdAt,
                        // Campos adicionales para compatibilidad con la tabla
                        REGISTRATION_DATE: moment(item.createdAt).format("DD/MM/YYYY"),
                        NAME: item.fullname,
                        ARRIVAL_DATE: moment(item.arrival_datetime).format("DD/MM/YYYY"),
                        ARRIVAL_FLIGHT: item.arrival_flight_number,
                        DEPARTURE_DATE: moment(item.departure_datetime).format("DD/MM/YYYY"),
                        TRANSPORT: item.unit,
                    }));

                    this.setState({
                        reservations: mappedReservations,
                        isloading: false
                    });
                } else {
                    this.setState({
                        reservations: [],
                        isloading: false
                    });
                }
            })
            .catch((error) => {
                console.error("Error fetching public reservations:", error);
                this.setState({ isloading: false });

                Swal.fire({
                    icon: "error",
                    title: "Service Unavailable / Servicio No Disponible",
                    text: "The reservations service is temporarily unavailable. Please try again in a few minutes. / El servicio de reservaciones no está disponible temporalmente. Por favor, intente nuevamente en unos minutos.",
                    confirmButtonText: "OK"
                });
            });
    };

    handleDetails = (record) => {
        this.setState({
            selected_res: record,
            modalIsOpen: true
        });
    };

    toggle = () => {
        this.setState({
            modalIsOpen: !this.state.modalIsOpen
        });
    };

    exportExcel = () => {
        const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
        const fileExtension = '.xlsx';
        const fileName = `public-reservations-${moment().format('YYYY-MM-DD')}`;

        const ws = XLSX.utils.json_to_sheet(this.state.reservations);
        const wb = { Sheets: { 'data': ws }, SheetNames: ['data'] };
        const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        const data = new Blob([excelBuffer], { type: fileType });
        FileSaver.saveAs(data, fileName + fileExtension);
    };

    render() {
        const columns = [
            {
                key: "ID",
                text: "ID Reserva",
                align: "center",
                sortable: true,
            },
            {
                key: "FOLIO",
                text: "Folio",
                align: "center",
                sortable: true,
            },
            {
                key: "REGISTRATION_DATE",
                text: "Registro",
                align: "center",
                sortable: true,
            },
            {
                key: "NAME",
                text: "Cliente",
                align: "center",
                sortable: true,
            },
            {
                key: "MEMBER_ID",
                text: "Member ID",
                align: "center",
                sortable: true,
            },
            {
                key: "ARRIVAL_DATE",
                text: "Llegada",
                align: "center",
                sortable: true,
            },
            {
                key: "ARRIVAL_FLIGHT",
                text: "Vuelo",
                align: "center",
                sortable: true,
            },
            {
                key: "DEPARTURE_DATE",
                text: "Salida",
                align: "center",
                sortable: true,
            },
            {
                key: "TRANSPORT",
                text: "Transporte",
                align: "center",
                sortable: true,
            },
            {
                key: "TOTAL_PAYMENT",
                text: "Total",
                align: "center",
                sortable: true,
                cell: (record) => `$${record.TOTAL_PAYMENT} USD`
            },
            {
                key: "action",
                text: "Opciones",
                width: 125,
                align: "center",
                className: "action",
                sortable: false,
                cell: (record) => (
                    <Fragment key={record.ID}>
                        <button
                            className="btn btn-dark btn-sm"
                            style={{ marginRight: "4px" }}
                            onClick={() => this.handleDetails(record)}
                        >
                            <i className="fa fa-info-circle"></i>
                        </button>
                    </Fragment>
                ),
            },
        ];

        const config = {
            page_size: 20,
            length_menu: [20, 50, 100, 200],
            button: { excel: false },
            sort: { column: "ID", order: "desc" },
        };

        return (
            <LoadingOverlay active={this.state.isloading} styles={loaderStyles} spinner text="Loading...">
                <div className="App">
                    <Header></Header>
                    <div className="full" id="breadcrumbs">
                        <div className="grid-container">
                            <div className="contenedor-flex grid-x grid-padding-x"></div>
                        </div>
                    </div>
                    <div className="grid-container">
                        <div className="grid-x grid-padding-x grid-padding-y">
                            <div className="small-12 cell">
                                <h1 style={{textTransform:"uppercase"}}>Public Reservations</h1>
                                <p>View all public reservations from the booking system.</p>
                            </div>
                        </div>
                    </div>

                    <div className="grid-container">
                        <div className="grid-x grid-padding-x grid-padding-y"></div>
                        <div className="grid-x grid-padding-x grid-padding-y b-white">
                            <div className="small-12 cell">
                                <div className="medium-4 cell d-flex justify-content-end align-items-end" style={{ marginBottom: "10px" }}>
                                    <button type="button" className="button btn-green" onClick={this.exportExcel}>
                                        Export to Excel
                                    </button>
                                </div>

                                <ReactDatatable
                                    className="table stack b-white hover table-bordered"
                                    key={"public-reservations-table"}
                                    config={config}
                                    records={this.state.reservations}
                                    columns={columns}
                                />
                            </div>
                        </div>
                    </div>
                    <Footer></Footer>
                </div>

                {/* Modal para mostrar detalles de la reservación */}
                <Modal isOpen={this.state.modalIsOpen} toggle={this.toggle} size="lg" scrollable={true} className="modal-show-info">
                    <ModalHeader toggle={this.toggle}>
                        RESERVATION DETAILS - ID: {this.state.selected_res.FOLIO}
                    </ModalHeader>
                    <ModalBody>
                        <div className="grid-container">
                            <div className="grid-x grid-padding-x">
                                <div className="small-12 medium-12 cell text-left">
                                    <div className="grid-x">
                                        <div className="small-12 medium-6 cell info-fix">
                                            <h4>Folio: <span className="promotion_text">{this.state.selected_res.FOLIO}</span></h4>
                                            {this.state.selected_res.DISCOUNT_PERCENT > 0 && (
                                                <p>Discount: <strong>{this.state.selected_res.DISCOUNT_PERCENT}% off</strong></p>
                                            )}
                                            <p><strong>Trip Type:</strong> {this.state.selected_res.TRIP_TYPE}</p>
                                            <p><strong>Unit:</strong> {this.state.selected_res.UNIT}</p>
                                            <p><strong>Total Passengers:</strong> {this.state.selected_res.TOTAL_PASSENGERS}</p>

                                            <h5>Customer Information</h5>
                                            <p><strong>Name:</strong> {this.state.selected_res.FULLNAME}</p>
                                            <p><strong>Member ID:</strong> {this.state.selected_res.MEMBER_ID}</p>
                                            <p><strong>Email:</strong> {this.state.selected_res.EMAIL}</p>
                                            <p><strong>Phone:</strong> {this.state.selected_res.CELLPHONE}</p>

                                            <h5>Locations</h5>
                                            <p><strong>Pickup:</strong> {this.state.selected_res.PICKUP_LOCATION}</p>
                                            <p><strong>Destination:</strong> {this.state.selected_res.DESTINATION_LOCATION}</p>
                                        </div>

                                        <div className="small-12 medium-6 cell info-fix">
                                            <h5>Flight Information</h5>
                                            <p><strong>Arrival:</strong> {this.state.selected_res.ARRIVAL_DATETIME}</p>
                                            <p><strong>Arrival Airline:</strong> {this.state.selected_res.ARRIVAL_AIRLINE}</p>
                                            <p><strong>Arrival Flight:</strong> {this.state.selected_res.ARRIVAL_FLIGHT_NUMBER}</p>

                                            <p><strong>Departure:</strong> {this.state.selected_res.DEPARTURE_DATETIME}</p>
                                            <p><strong>Departure Airline:</strong> {this.state.selected_res.DEPARTURE_AIRLINE}</p>
                                            <p><strong>Departure Flight:</strong> {this.state.selected_res.DEPARTURE_FLIGHT_NUMBER}</p>
                                            <p><strong>Hotel Departure Time:</strong> {this.state.selected_res.HOTEL_DEPARTURE_TIME}</p>

                                            {this.state.selected_res.EXTRA_SERVICE && (
                                                <>
                                                    <h5>Extra Service</h5>
                                                    <p><strong>Service:</strong> {this.state.selected_res.EXTRA_SERVICE.name}</p>
                                                    <p><strong>Time:</strong> {this.state.selected_res.EXTRA_SERVICE.time}</p>
                                                    <p><strong>Price:</strong> ${this.state.selected_res.EXTRA_SERVICE.price} USD</p>
                                                </>
                                            )}

                                            <h5>Payment Information</h5>
                                            <p><strong>Payment ID:</strong> {this.state.selected_res.PAYMENT_ID}</p>
                                            <p><strong>Payment Method:</strong> {this.state.selected_res.PAYMENT_METHOD}</p>
                                            <p><strong>Base Price:</strong> ${this.state.selected_res.BASE_PRICE} USD</p>
                                            <p><strong>RCI Fee:</strong> ${this.state.selected_res.FEE_RCI} USD</p>
                                            <p><strong>TR Fee:</strong> ${this.state.selected_res.FEE_TR} USD</p>
                                            <p><strong>Total Payment:</strong> <span className="promotion_text">${this.state.selected_res.TOTAL_PAYMENT} USD</span></p>

                                            {this.state.selected_res.OBSERVATIONS && (
                                                <>
                                                    <h5>Observations</h5>
                                                    <p>{this.state.selected_res.OBSERVATIONS}</p>
                                                </>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ModalBody>
                    <ModalFooter>
                        <button className="btn btn-secondary" onClick={this.toggle}>
                            Close
                        </button>
                    </ModalFooter>
                </Modal>
            </LoadingOverlay>
        );
    }
}

export default PublicReservations;