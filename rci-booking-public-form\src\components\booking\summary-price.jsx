import React, { useContext, useEffect, useState } from 'react';
import styles from './summary-price.module.scss';
import { ReservationContext } from '../../context/reservation.context';
import { Spinner } from 'reactstrap';

const SummaryPrice = ({ reserve, isServiceActive, totalPayment, serviceSelected, isCalculatingRate, tarifaBase, rateData }) => {
    const { reservation } = useContext(ReservationContext);
    const { has_promotion: hasPromotion, promotion } = reservation;
    const [regularRate, setRegularRate] = useState(0);
    const [promotionRate, setPromotionRate] = useState(0);
    const [extraServicePrice, setExtraServicePrice] = useState(0);
    const [promotionPercent, setPromotionPercent] = useState(promotion);
    const [baseTransportRate, setBaseTransportRate] = useState(0);

    // Estados para el banner promocional
    const [oneWayRate, setOneWayRate] = useState(0);
    const [discountPercent, setDiscountPercent] = useState(0);
    const [savings, setSavings] = useState(0);
    
    // console.log('tarifaBase', tarifaBase);

    // Efecto para actualizar el precio del servicio extra
    useEffect(() => {
      if (isServiceActive && serviceSelected && serviceSelected.price) {
        // Aseguramos que el precio del servicio extra sea un número
        setExtraServicePrice(Number(serviceSelected.price));
      } else {
        setExtraServicePrice(0);
      }
    }, [isServiceActive, serviceSelected]);

    // Efecto para calcular el precio base de transporte (sin servicio extra)
    useEffect(() => {
      // Si hay un servicio activo, restamos su precio del total para obtener el precio base de transporte
      if (isServiceActive && serviceSelected && serviceSelected.price && totalPayment > 0) {
        const transportRate = Number(totalPayment) - Number(serviceSelected.price);
        setBaseTransportRate(Number(transportRate));
      } else {
        // Si no hay servicio activo, el precio base es igual al totalPayment
        // Aseguramos que baseTransportRate sea siempre un número
        setBaseTransportRate(Number(totalPayment));
      }
    }, [totalPayment, isServiceActive, serviceSelected]);

    // Efecto para calcular el precio regular y el precio con promoción
    useEffect(() => {
      if (baseTransportRate > 0) {
        // El precio regular es el precio base de transporte
        setRegularRate(Number(baseTransportRate));
        setPromotionRate(Number(baseTransportRate));
      } else {
        setRegularRate(0);
        setPromotionRate(0);
      }
    }, [baseTransportRate, hasPromotion, promotionPercent, isServiceActive]);

    // Efecto para actualizar el porcentaje de promoción cuando cambia en el contexto
    useEffect(() => {
      setPromotionPercent(Number(promotion));
    }, [promotion]);

    // Efecto para calcular datos del banner promocional
    useEffect(() => {
      if (rateData && reserve.trip_type === "Round Trip") {
        const oneWayPrice = parseFloat(rateData.oneWay || 0);
        const roundTripPrice = parseFloat(rateData.roundTrip || 0);

        if (oneWayPrice > 0 && roundTripPrice > 0) {
          // Aplicar promoción si existe
          let finalOneWayPrice = oneWayPrice;
          let finalRoundTripPrice = roundTripPrice;

          if (hasPromotion && promotionPercent > 0) {
            // Aplicar descuento a ambas tarifas
            const discountMultiplier = (100 - promotionPercent) / 100;
            finalOneWayPrice = oneWayPrice * discountMultiplier;
            finalRoundTripPrice = roundTripPrice * discountMultiplier;
          }

          // Calcular el precio de dos viajes one way con promoción aplicada
          const twoOneWayPrice = finalOneWayPrice * 2;

          // Calcular el descuento entre round trip vs dos one way
          const discount = twoOneWayPrice - finalRoundTripPrice;
          const discountPercentage = Math.round((discount / twoOneWayPrice) * 100);

          setOneWayRate(twoOneWayPrice);
          setDiscountPercent(discountPercentage);
          setSavings(discount);
        }
      } else {
        // Reset values if not Round Trip
        setOneWayRate(0);
        setDiscountPercent(0);
        setSavings(0);
      }
    }, [rateData, reserve.trip_type, hasPromotion, promotionPercent]);

  return (<>
    <div className={styles.reservationSummary}>
      <div className={styles.summaryServiceTitle}>
        <h4 className={styles.itemTitle}>
          Service: <span>{reserve.trip_type}</span>
          {isCalculatingRate && <Spinner size="sm" color="primary" className="ml-2" />}
        </h4>
      </div>
        {hasPromotion && (
            <div className={`${styles.summaryItem} ${styles.savings}`}>
                <h5 className={styles.savingsText}>Promotion: <span className={styles.savingsText}>{promotionPercent}%</span> Off</h5>
            </div>
        )}
         {totalPayment !== 0 && (<>
            <div className={styles.summaryItem}>
                <span className={styles.itemTitle}>Rate:</span>
                <span className={ hasPromotion ? `${styles.itemValue} ${styles.originalPrice}} line-through` : `${styles.itemValue}`}>
                  ${tarifaBase || reserve.base_price || reserve.total_payment} USD
                </span>
            </div>
            { hasPromotion && (
              <div className={`${styles.summaryItem} ${styles.savings}`}>
                  <span className={styles.itemTitle}>Promotion Rate:</span>
                  <span style={{fontSize:'1.25rem'}} className={`${styles.itemValue} ${styles.savingsText}`}>
                    ${Math.round(promotionRate)} USD
                  </span>
              </div>
              )
            }
         </>)}
      {isServiceActive && serviceSelected && (
        <>
        <hr />
        <div className={styles.extra_service_active}>
            <div>
                <h5 className={styles.itemTitle}>Extra Service:</h5>
                <div className={`${styles.itemValue} ${styles.name}`}>
                  {serviceSelected.name || "Stop at the Supermarket or Grocery Store"}
                  <p>{serviceSelected.time}</p>
                </div>
            </div>
            <div className={`${styles.itemTitle} ${styles.price}`}>
              ${Math.round(Number(extraServicePrice))} USD
            </div>
        </div>
        <hr />
        </>
      )}
      <div className={styles.summaryItem}>
        <span className={`${styles.itemTitle} ${styles.totalTitle}`}>Total:</span>
        {
          hasPromotion ?
          <div className={`${styles.itemValue} ${styles.totalPrice} ${styles.savings}`}>
            <span className={styles.savingsText}>
              ${Math.round(isServiceActive
                ? Number(promotionRate) + Number(extraServicePrice)
                : Number(promotionRate))} USD
            </span>
          </div>
          :
          <span className={`${styles.itemValue} ${styles.totalPrice}`}>
            ${Math.round(isServiceActive
              ? Number(regularRate) + Number(extraServicePrice)
              : Number(regularRate))} USD
          </span>
        }
      </div>
    </div>

    {/* Banner promocional para Round Trip */}
    {reserve.trip_type === "Round Trip" && discountPercent > 0 && (
      <div className={styles.promotionalBanner}>
        <div className={styles.bannerHeader}>
          <span className={styles.checkIcon}>✓</span>
          <span className={styles.bestValue}>Best Value</span>
        </div>
        <div className={styles.bannerContent}>
          <div className={styles.oneWayPrice}>
            One way price: <span className={`${styles.priceValue} line-through`}>${oneWayRate.toFixed(2)}</span>
          </div>
          <div className={styles.savings}>
            <span className={styles.savingsText}>
              Save ${savings.toFixed(2)} ({discountPercent}% off) vs. booking <span>two One-Way trips</span>
            </span>
          </div>
        </div>
      </div>
    )}
    </>
  );
};

SummaryPrice.defaultProps = {
  serviceSelected: {}
};

export default SummaryPrice;
