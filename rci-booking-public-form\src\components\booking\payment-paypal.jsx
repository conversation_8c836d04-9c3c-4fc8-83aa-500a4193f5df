import React, { useEffect, useState } from 'react'
import { PayPalButtons, PayPalScriptProvider, usePayPalScriptReducer } from '@paypal/react-paypal-js';
import configs from "../../config/config";

function Message({ content }) {
    return <p>{content}</p>;
}

export const PaymentPaypal = () => {
    const [message, setMessage] = useState("");
    const [reserve, setIdReserve] = useState({});
    const [ordenData, setOrdenData] = useState([]);
    useEffect(() => {
      setIdReserve({
      ...reserve,
      idReservation: localStorage.getItem('idReservation'),
      amount: localStorage.getItem('details'),
      description:localStorage.getItem('desc')
    })
  }, []);
    const initialOptions = {
        "client-id": "AXhD7yVYu1Gtn9_Qyu479rb9VBl4z_e9NqJm-cy8e7jrWJQtTSRjDGF7xswb9qZb7jMFv1UeMO_CMxDo",
        "enable-funding": "venmo",
        "disable-funding": "paylater",
        "data-sdk-integration-source": "integrationbuilder_sc",
    };

      return (
        <div className="App">
          <PayPalScriptProvider options={initialOptions}>
            <PayPalButtons
              style={{
                shape: "rect",
                layout: "horizontal",
              }}
              createOrder={ async () => {
                try {
                  const response = await fetch(`${configs.URL_PAYMENT_PAYPAL}/api/orders`, {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                    },
                    // use the "body" param to optionally pass additional order information
                    // like product ids and quantities
                    body: JSON.stringify({
                      cart: [
                        {
                            id: reserve.idReservation,
                            quantity:1,
                            description:reserve.description,
                            amount: {
                                "currency_code": "USD",
                                "value": reserve.amount,
                            }
                        },
                      ],
                    }),
                  });

                  const orderData = await response.json();
                  // console.log(orderData);

                  if (orderData.id) {
                    return orderData.id;
                  } else {
                    const errorDetail = orderData?.details?.[0];
                    const errorMessage = errorDetail
                      ? `${errorDetail.issue} ${errorDetail.description} (${orderData.debug_id})`
                      : JSON.stringify(orderData);

                    throw new Error(errorMessage);
                  }
                } catch (error) {
                  // console.error(error);
                  setMessage(`Could not initiate PayPal Checkout...${error}`);
                }
              }}
              onApprove={async (data, actions) => {
                try {
                  const response = await fetch(
                    `${configs.URL_PAYMENT_PAYPAL}/api/orders/${data.orderID}/capture`,
                    {
                      method: "POST",
                      headers: {
                        "Content-Type": "application/json",
                      },
                    },
                  );

                  const orderData = await response.json();
                  // Three cases to handle:
                  //   (1) Recoverable INSTRUMENT_DECLINED -> call actions.restart()
                  //   (2) Other non-recoverable errors -> Show a failure message
                  //   (3) Successful transaction -> Show confirmation or thank you message

                  // console.log(orderData);
                  const errorDetail = orderData?.details?.[0];
                  // console.log(errorDetail);

                  if (errorDetail?.issue === "INSTRUMENT_DECLINED") {
                    // (1) Recoverable INSTRUMENT_DECLINED -> call actions.restart()
                    // recoverable state, per https://developer.paypal.com/docs/checkout/standard/customize/handle-funding-failures/
                    return actions.restart();
                  } else if (errorDetail) {
                    // (2) Other non-recoverable errors -> Show a failure message
                    throw new Error(
                      `${errorDetail.description} (${orderData.debug_id})`,
                    );
                  } else {
                    // (3) Successful transaction -> Show confirmation or thank you message
                    // Or go to another URL:  actions.redirect('thank_you.html');
                    const transaction = orderData.purchase_units[0].payments.captures[0];
                    // console.log(transaction);
                    setMessage(
                      `Transaction ${transaction.status}: ${transaction.id}.`,
                    );
                    // console.log(
                    //   "Capture result",
                    //   orderData,
                    //   JSON.stringify(orderData, null, 2),
                    // );
                  }
                } catch (error) {
                  // console.error(error);
                  setMessage(
                    `Sorry, your transaction could not be processed...${error}`,
                  );
                }
              }}
            />
          </PayPalScriptProvider>
          <Message content={message} />
        </div>
      );
}
